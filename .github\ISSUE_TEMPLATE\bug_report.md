---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Hardware Setup:**
 - Arduino Board: [e.g. Uno, Nano]
 - JoystickShield Version: [if known]
 - USB Cable: [e.g. original Arduino cable, third-party]

**Desktop (please complete the following information):**
 - OS: [e.g. Windows 10, Windows 11]
 - Python Version: [e.g. 3.9.0]
 - Application Version: [e.g. v1.0.0]

**Serial Communication:**
 - COM Port: [e.g. COM3, COM4]
 - Baud Rate: [e.g. 9600]
 - Connection Status: [e.g. Connected, Failed to connect]

**Additional context**
Add any other context about the problem here.

**Error Messages**
If applicable, paste any error messages or logs here:
```
[Paste error messages here]
```
