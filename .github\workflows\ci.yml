name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags:
      - 'v*'  # 匹配 v1.0.0, v2.1.3 等格式的 tag
  pull_request:
    branches: [ main ]

jobs:
  test-python:
    name: Test Python Application
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest flake8
    
    # - name: Lint with flake8
    #   run: |
    #     # Stop the build if there are Python syntax errors or undefined names
    #     flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    #     # Exit-zero treats all errors as warnings
    #     flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Test Python syntax
      run: |
        python -m py_compile joystick_controller_final.py
        python -m py_compile input_method_manager.py

  build-arduino:
    name: Build Arduino Firmware
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Cache pip
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Cache PlatformIO
      uses: actions/cache@v4
      with:
        path: ~/.platformio
        key: ${{ runner.os }}-${{ hashFiles('**/lockfiles') }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install PlatformIO
      run: |
        python -m pip install --upgrade pip
        pip install --upgrade platformio
    
    - name: Build Arduino firmware
      run: pio run
    
    - name: Upload firmware artifacts
      uses: actions/upload-artifact@v4
      with:
        name: arduino-firmware
        path: .pio/build/uno/firmware.hex

  build-executable:
    name: Build Windows Executable
    runs-on: windows-latest
    needs: test-python
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Build executable with PyInstaller
      run: |
        pyinstaller --onefile --name JoystickController joystick_controller_final.py
    
    - name: Upload executable artifact
      uses: actions/upload-artifact@v4
      with:
        name: windows-executable
        path: dist/JoystickController.exe

  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [test-python, build-arduino, build-executable]
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - uses: actions/checkout@v4

    - name: Get tag name
      id: tag
      run: echo "tag_name=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

    - name: Download Arduino firmware
      uses: actions/download-artifact@v4
      with:
        name: arduino-firmware
        path: ./artifacts/

    - name: Download Windows executable
      uses: actions/download-artifact@v4
      with:
        name: windows-executable
        path: ./artifacts/

    - name: Create release package
      run: |
        mkdir -p release-package
        cp -r artifacts/* release-package/
        cp README.md release-package/
        cp requirements.txt release-package/
        cp platformio.ini release-package/
        cp src/main.cpp release-package/
        cp *.bat release-package/ || true
        tar -czf GameBoard-${{ steps.tag.outputs.tag_name }}.tar.gz release-package/

    - name: Create Release
      uses: softprops/action-gh-release@v2
      with:
        files: GameBoard-${{ steps.tag.outputs.tag_name }}.tar.gz
        generate_release_notes: true
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
