name: Documentation

on:
  push:
    branches: [ main ]
    paths:
      - '**.md'
      - 'docs/**'
  pull_request:
    branches: [ main ]
    paths:
      - '**.md'
      - 'docs/**'

jobs:
  check-links:
    name: Check Documentation Links
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check markdown links
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
        config-file: '.github/markdown-link-check-config.json'
        folder-path: '.'
        file-path: './README.md'
        
  lint-markdown:
    name: Lint Markdown
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Lint markdown files
      uses: David<PERSON><PERSON>/markdownlint-cli2-action@v9
      with:
        globs: '**/*.md'
        
  spell-check:
    name: Spell Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Spell check
      uses: streetsidesoftware/cspell-action@v2
      with:
        files: '**/*.md'
        incremental_files_only: false
