name: Documentation

on:
  push:
    branches: [ main ]
    paths:
      - '**.md'
      - 'docs/**'
  pull_request:
    branches: [ main ]
    paths:
      - '**.md'
      - 'docs/**'

jobs:
  check-links:
    name: Check Documentation Links
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check markdown links
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
        config-file: '.github/markdown-link-check-config.json'
        folder-path: '.'
        file-path: './README.md'
        
      
