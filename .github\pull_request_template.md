## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Code refactoring
- [ ] Performance improvement

## Changes Made
- [ ] Arduino code changes
- [ ] Python application changes
- [ ] Documentation updates
- [ ] Configuration changes
- [ ] Dependencies updated

## Testing
- [ ] I have tested this change locally
- [ ] I have tested with actual hardware
- [ ] I have tested on Windows
- [ ] All existing tests pass
- [ ] I have added new tests for new functionality

## Hardware Tested
- [ ] Arduino Uno
- [ ] Arduino Nano
- [ ] JoystickShield v1.x
- [ ] JoystickShield v2.x
- [ ] Other: ___________

## Games Tested
List any games you tested this change with:
- [ ] Game 1: ___________
- [ ] Game 2: ___________
- [ ] Game 3: ___________

## Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] Any dependent changes have been merged and published

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Additional Notes
Any additional information that reviewers should know.
