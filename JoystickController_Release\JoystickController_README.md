# JoystickController - 游戏手柄控制器

## 📁 文件说明

### 可执行文件
- **`dist/JoystickController.exe`** - 主程序可执行文件 (8.8MB)
- **`run_joystick_controller.bat`** - 便捷启动脚本

### 源代码文件
- **`joystick_controller_final.py`** - 主程序源代码
- **`input_method_manager.py`** - 输入法管理模块
- **`joystick_controller.spec`** - PyInstaller配置文件

## 🚀 快速开始

### 方法1: 使用批处理文件 (推荐)
双击 `run_joystick_controller.bat` 文件

### 方法2: 直接运行
双击 `dist/JoystickController.exe` 文件

### 方法3: 命令行运行
```cmd
cd /d "程序所在目录"
dist\JoystickController.exe
```

## 🎮 按键映射

### 摇杆方向控制
- **摇杆上** → W键
- **摇杆下** → S键  
- **摇杆左** → A键
- **摇杆右** → D键
- **摇杆左上** → A+W键
- **摇杆右上** → D+W键
- **摇杆左下** → A+S键
- **摇杆右下** → D+S键

### 按钮控制
- **摇杆按钮** → F键
- **上按钮** → O键
- **下按钮** → J键
- **左按钮** → I键
- **右按钮** → K键
- **E按钮** → E键
- **F按钮** → V键

## ⚙️ 系统要求

- **操作系统**: Windows 10/11
- **硬件**: Arduino兼容的游戏手柄控制器
- **连接**: USB串口连接
- **权限**: 建议以管理员身份运行以获得最佳兼容性

## 🔧 使用说明

1. **连接硬件**: 将Arduino游戏手柄通过USB连接到电脑
2. **运行程序**: 双击批处理文件或可执行文件
3. **自动检测**: 程序会自动搜索并连接到Arduino设备
4. **切换游戏**: 确保游戏窗口处于活动状态
5. **开始游戏**: 使用手柄控制游戏

## ⚠️ 重要提示

1. **游戏窗口焦点**: 请确保游戏窗口处于活动状态
2. **窗口模式**: 建议将游戏设置为窗口模式以获得更好的兼容性
3. **输入法**: 程序会自动尝试切换到英文输入法
4. **管理员权限**: 如果遇到兼容性问题，请右键"以管理员身份运行"
5. **退出程序**: 按 Ctrl+C 退出程序

## 🐛 故障排除

### 无法连接到设备
- 检查USB连接是否正常
- 确认Arduino设备驱动已正确安装
- 尝试更换USB端口

### 游戏无响应
- 确保游戏窗口处于前台
- 检查游戏的按键设置
- 尝试以管理员身份运行程序

### 程序无法启动
- 确认Windows系统版本兼容
- 检查是否有杀毒软件阻止运行
- 尝试在命令行中运行查看错误信息

## 📝 版本信息

- **版本**: Final Game Version
- **构建工具**: PyInstaller 6.14.2
- **Python版本**: 3.11.7
- **支持平台**: Windows 64位

## 🔄 更新日志

### 当前版本特性
- ✅ 自动设备检测和连接
- ✅ 实时按键映射
- ✅ 输入法自动切换
- ✅ 多种输入方法支持 (Win32 API + keyboard库)
- ✅ 方向键超时自动释放
- ✅ 管理员权限检测
- ✅ 详细的状态显示和错误提示

---

**注意**: 此程序专为游戏控制优化，确保在使用前已正确连接Arduino游戏手柄设备。
