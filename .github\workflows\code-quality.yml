name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run weekly security scan
    - cron: '0 0 * * 0'

jobs:
  code-quality:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort bandit safety
        pip install -r requirements.txt
    
    # - name: Check code formatting with Black
    #   run: |
    #     black --check --diff .
    #   continue-on-error: true

    # - name: Check import sorting with isort
    #   run: |
    #     isort --check-only --diff .
    #   continue-on-error: true

    # - name: Lint with flake8
    #   run: |
    #     # Stop the build if there are Python syntax errors or undefined names
    #     flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    #     # Exit-zero treats all errors as warnings
    #     flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Security check with bandit
      run: |
        bandit -r . -f json -o bandit-report.json || true
        bandit -r . || true
      continue-on-error: true
    
    - name: Check dependencies for security vulnerabilities
      run: |
        safety check --json --output safety-report.json || true
        safety check || true
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  arduino-check:
    name: Arduino Code Check
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    # - name: Arduino Lint
    #   uses: arduino/arduino-lint-action@v1
    #   with:
    #     path: ./src
        
    - name: Check Arduino code compilation
      run: |
        # Install Arduino CLI
        curl -fsSL https://raw.githubusercontent.com/arduino/arduino-cli/master/install.sh | sh
        export PATH=$PATH:$PWD/bin
        
        # Initialize Arduino CLI
        arduino-cli core update-index
        arduino-cli core install arduino:avr
        
        # Compile the sketch
        arduino-cli compile --fqbn arduino:avr:uno src/main.cpp --output-dir build/
      continue-on-error: true

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
    
    - name: Dependency Review
      uses: actions/dependency-review-action@v3
      with:
        fail-on-severity: moderate
